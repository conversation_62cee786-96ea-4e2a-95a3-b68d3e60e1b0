"use client"

import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { cn } from "@/lib/utils"

const Accordion = AccordionPrimitive.Root

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn("", className)}
    {...props}
  />
))
AccordionItem.displayName = "AccordionItem"

interface AccordionTriggerProps
  extends React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger> {
  openIcon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  closeIcon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  IconClass?: string;
  FrontArrowIcon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}
const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  AccordionTriggerProps
>(
  (
    {
      className,
      children,
      openIcon: OpenIcon,
      closeIcon: CloseIcon,
      IconClass,
      FrontArrowIcon:FrontArrowIcon,
      ...props
    },
    ref
  ) => (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        ref={ref}
        className={cn(
          "group flex flex-1 items-center  py-4 text-sm font-medium transition-all hover:underline text-left",
          className
        )}
        {...props}
      >
        {FrontArrowIcon && (
          <FrontArrowIcon
            className={`h-9 w-9 group-data-[state=open]:rotate-0 group-data-[state=closed]:rotate-180 transition-all`}
            aria-hidden="true"
          />
        )}
        {children}
        {CloseIcon && (
          <CloseIcon
            className={`h-5 w-5 text-primaryColor group-data-[state=open]:hidden ${IconClass}`}
            aria-hidden="true"
          />
        )}
        {OpenIcon && (
          <OpenIcon
            className={`h-5 w-5 text-primaryColor group-data-[state=closed]:hidden ${IconClass}`}
            aria-hidden="true"
          />
        )}
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  )
);

AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn("pb-4 pt-0", className)}>{children}</div>
  </AccordionPrimitive.Content>
))
AccordionContent.displayName = AccordionPrimitive.Content.displayName

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }
